#!/usr/bin/env python3
"""
Email Notification Service Module

This module provides email notification functionality for the TNGD backup system.
It handles sending backup reports, error notifications, and status updates via SMTP.

Features:
- Secure SMTP authentication with Gmail App Passwords
- HTML and plain text email templates
- Mock mode for testing without sending actual emails
- Comprehensive error handling and logging
- Support for backup summaries and detailed reports
"""

import os
import logging
import smtplib
import json
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

# Configure logging
logger = logging.getLogger(__name__)

class NotificationService:
    """
    Email notification service for backup operations.
    
    Handles sending various types of notifications including:
    - Backup completion reports
    - Error notifications
    - Progress updates
    - Detailed summaries
    """
    
    def __init__(self, config_manager=None):
        """
        Initialize the notification service.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager
        self.email_settings = None
        self._load_email_settings()
    
    def _load_email_settings(self) -> None:
        """Load email settings from configuration manager."""
        if self.config_manager:
            self.email_settings = self.config_manager.get_email_settings()
            logger.info("Email settings loaded successfully")
        else:
            logger.warning("No configuration manager provided - email functionality disabled")
    
    def send_backup_report(self, backup_results: List[Dict[str, Any]], 
                          backup_type: str = "daily") -> bool:
        """
        Send a comprehensive backup report via email.
        
        Args:
            backup_results: List of backup operation results
            backup_type: Type of backup (daily, monthly, etc.)
            
        Returns:
            True if email sent successfully, False otherwise
        """
        try:
            if not self.email_settings:
                logger.error("Email settings not configured")
                return False
            
            # Generate report content
            subject, html_body, text_body = self._generate_backup_report(
                backup_results, backup_type
            )
            
            # Send the email
            return self._send_email(subject, html_body, text_body)
            
        except Exception as e:
            logger.error(f"Failed to send backup report: {str(e)}")
            return False
    
    def send_error_notification(self, error_details: Dict[str, Any], 
                               context: str = "") -> bool:
        """
        Send error notification email.
        
        Args:
            error_details: Dictionary containing error information
            context: Additional context about the error
            
        Returns:
            True if email sent successfully, False otherwise
        """
        try:
            if not self.email_settings:
                logger.error("Email settings not configured")
                return False
            
            # Generate error notification content
            subject = f"🚨 TNGD Backup System Error - {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            
            html_body = self._generate_error_html(error_details, context)
            text_body = self._generate_error_text(error_details, context)
            
            # Send the email
            return self._send_email(subject, html_body, text_body)
            
        except Exception as e:
            logger.error(f"Failed to send error notification: {str(e)}")
            return False
    
    def _generate_backup_report(self, backup_results: List[Dict[str, Any]], 
                               backup_type: str) -> Tuple[str, str, str]:
        """
        Generate backup report content in HTML and text formats.
        
        Args:
            backup_results: List of backup operation results
            backup_type: Type of backup
            
        Returns:
            Tuple of (subject, html_body, text_body)
        """
        # Calculate summary statistics
        total_operations = len(backup_results)
        successful_operations = sum(1 for result in backup_results if result.get('status') == 'completed')
        failed_operations = total_operations - successful_operations
        success_rate = (successful_operations / total_operations * 100) if total_operations > 0 else 0
        
        # Calculate total data processed
        total_data_mb = sum(result.get('file_size_mb', 0) for result in backup_results)
        total_duration = sum(result.get('duration_seconds', 0) for result in backup_results)
        
        # Generate subject
        status_emoji = "✅" if failed_operations == 0 else "⚠️" if failed_operations < total_operations else "❌"
        subject = f"{status_emoji} TNGD {backup_type.title()} Backup Report - {datetime.now().strftime('%Y-%m-%d')}"
        
        # Generate HTML body
        html_body = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f8ff; padding: 20px; border-radius: 5px; }}
                .summary {{ background-color: #f9f9f9; padding: 15px; margin: 20px 0; border-radius: 5px; }}
                .success {{ color: #28a745; }}
                .warning {{ color: #ffc107; }}
                .error {{ color: #dc3545; }}
                table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .status-success {{ background-color: #d4edda; }}
                .status-failed {{ background-color: #f8d7da; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h2>{status_emoji} TNGD {backup_type.title()} Backup Report</h2>
                <p><strong>Date:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
            
            <div class="summary">
                <h3>📊 Summary</h3>
                <ul>
                    <li><strong>Total Operations:</strong> {total_operations}</li>
                    <li><strong>Successful:</strong> <span class="success">{successful_operations}</span></li>
                    <li><strong>Failed:</strong> <span class="error">{failed_operations}</span></li>
                    <li><strong>Success Rate:</strong> {success_rate:.1f}%</li>
                    <li><strong>Total Data Processed:</strong> {total_data_mb:.2f} MB</li>
                    <li><strong>Total Duration:</strong> {total_duration:.1f} seconds</li>
                </ul>
            </div>
        """
        
        # Add detailed results table
        if backup_results:
            html_body += """
            <h3>📋 Detailed Results</h3>
            <table>
                <tr>
                    <th>Table Name</th>
                    <th>Date</th>
                    <th>Status</th>
                    <th>File Size (MB)</th>
                    <th>Duration (s)</th>
                    <th>Upload Path</th>
                </tr>
            """
            
            for result in backup_results:
                status_class = "status-success" if result.get('status') == 'completed' else "status-failed"
                status_text = "✅ Success" if result.get('status') == 'completed' else "❌ Failed"
                
                html_body += f"""
                <tr class="{status_class}">
                    <td>{result.get('table_name', 'N/A')}</td>
                    <td>{result.get('target_date', 'N/A')}</td>
                    <td>{status_text}</td>
                    <td>{result.get('file_size_mb', 0):.2f}</td>
                    <td>{result.get('duration_seconds', 0):.1f}</td>
                    <td>{result.get('upload_path', 'N/A')}</td>
                </tr>
                """
            
            html_body += "</table>"
        
        # Add failed operations details if any
        failed_results = [r for r in backup_results if r.get('status') != 'completed']
        if failed_results:
            html_body += """
            <h3>❌ Failed Operations</h3>
            <ul>
            """
            for result in failed_results:
                html_body += f"<li><strong>{result.get('table_name', 'Unknown')}:</strong> {result.get('error', 'Unknown error')}</li>"
            html_body += "</ul>"
        
        html_body += """
            <hr>
            <p><em>This is an automated report from the TNGD Backup System.</em></p>
        </body>
        </html>
        """
        
        # Generate text body
        text_body = f"""
TNGD {backup_type.title()} Backup Report
Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

SUMMARY:
- Total Operations: {total_operations}
- Successful: {successful_operations}
- Failed: {failed_operations}
- Success Rate: {success_rate:.1f}%
- Total Data Processed: {total_data_mb:.2f} MB
- Total Duration: {total_duration:.1f} seconds

DETAILED RESULTS:
"""
        
        for result in backup_results:
            status_text = "SUCCESS" if result.get('status') == 'completed' else "FAILED"
            text_body += f"""
- {result.get('table_name', 'N/A')} ({result.get('target_date', 'N/A')}): {status_text}
  Size: {result.get('file_size_mb', 0):.2f} MB, Duration: {result.get('duration_seconds', 0):.1f}s
  Path: {result.get('upload_path', 'N/A')}
"""
        
        if failed_results:
            text_body += "\nFAILED OPERATIONS:\n"
            for result in failed_results:
                text_body += f"- {result.get('table_name', 'Unknown')}: {result.get('error', 'Unknown error')}\n"
        
        text_body += "\n---\nThis is an automated report from the TNGD Backup System."
        
        return subject, html_body, text_body
    
    def _generate_error_html(self, error_details: Dict[str, Any], context: str) -> str:
        """Generate HTML content for error notification."""
        return f"""
        <html>
        <body style="font-family: Arial, sans-serif; margin: 20px;">
            <div style="background-color: #f8d7da; padding: 20px; border-radius: 5px; border-left: 5px solid #dc3545;">
                <h2 style="color: #721c24;">🚨 TNGD Backup System Error</h2>
                <p><strong>Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p><strong>Context:</strong> {context}</p>
                <p><strong>Error Type:</strong> {error_details.get('error_type', 'Unknown')}</p>
                <p><strong>Error Message:</strong> {error_details.get('error_message', 'No message available')}</p>
            </div>
            <p><em>Please check the system logs for more detailed information.</em></p>
        </body>
        </html>
        """
    
    def _generate_error_text(self, error_details: Dict[str, Any], context: str) -> str:
        """Generate text content for error notification."""
        return f"""
TNGD Backup System Error Alert

Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Context: {context}
Error Type: {error_details.get('error_type', 'Unknown')}
Error Message: {error_details.get('error_message', 'No message available')}

Please check the system logs for more detailed information.

---
This is an automated alert from the TNGD Backup System.
        """
    
    def _send_email(self, subject: str, html_body: str, text_body: str) -> bool:
        """
        Send email using SMTP.
        
        Args:
            subject: Email subject
            html_body: HTML email body
            text_body: Plain text email body
            
        Returns:
            True if email sent successfully, False otherwise
        """
        try:
            if not self.email_settings:
                logger.error("Email settings not configured")
                return False
            
            # Check if in mock mode
            if self.email_settings.get('mock_mode', True):
                logger.info("MOCK MODE: Email would be sent with the following details:")
                logger.info(f"To: {self.email_settings.get('email_to')}")
                logger.info(f"Subject: {subject}")
                logger.info(f"Body length: {len(text_body)} characters")
                return True
            
            # Create message
            msg = MIMEMultipart('alternative')
            msg['From'] = self.email_settings['email_from']
            msg['To'] = self.email_settings['email_to']
            msg['Subject'] = subject
            
            # Add both plain text and HTML parts
            text_part = MIMEText(text_body, 'plain')
            html_part = MIMEText(html_body, 'html')
            
            msg.attach(text_part)
            msg.attach(html_part)
            
            # Send email
            with smtplib.SMTP(self.email_settings['smtp_server'], 
                             int(self.email_settings['smtp_port'])) as server:
                server.starttls()
                server.login(self.email_settings['smtp_username'], 
                           self.email_settings['smtp_password'])
                server.send_message(msg)
            
            logger.info(f"Email sent successfully to {self.email_settings['email_to']}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email: {str(e)}")
            return False
    
    def test_email_configuration(self) -> bool:
        """
        Test email configuration by sending a test email.
        
        Returns:
            True if test email sent successfully, False otherwise
        """
        try:
            subject = "🧪 TNGD Backup System - Email Configuration Test"
            html_body = """
            <html>
            <body style="font-family: Arial, sans-serif; margin: 20px;">
                <div style="background-color: #d4edda; padding: 20px; border-radius: 5px;">
                    <h2 style="color: #155724;">✅ Email Configuration Test</h2>
                    <p>This is a test email to verify that your TNGD Backup System email configuration is working correctly.</p>
                    <p><strong>Test Time:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    <p>If you received this email, your email notifications are configured properly!</p>
                </div>
            </body>
            </html>
            """
            
            text_body = f"""
TNGD Backup System - Email Configuration Test

This is a test email to verify that your email configuration is working correctly.

Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

If you received this email, your email notifications are configured properly!

---
This is a test message from the TNGD Backup System.
            """
            
            return self._send_email(subject, html_body, text_body)
            
        except Exception as e:
            logger.error(f"Email configuration test failed: {str(e)}")
            return False
