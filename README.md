# TNGD Unified Backup System

## 🚀 Overview
The TNGD Unified Backup System is a **completely refactored** Python-based solution that eliminates all `.bat` file dependencies and provides a streamlined CLI interface for backing up Devo tables to OSS storage. This unified approach replaces the previous complex system of multiple batch files and scripts with a single, powerful Python script.

> **⚠️ MIGRATION NOTICE**: This system has been completely refactored. All previous batch files have been removed and replaced with the unified `tngd_backup.py` script. See [TNGD_UNIFIED_BACKUP_README.md](TNGD_UNIFIED_BACKUP_README.md) for complete migration guide.

## ✨ Key Features
- **🎯 Unified CLI Interface**: Single Python script replaces all batch files
- **📅 Flexible Date Input**: Support for today's data, specific dates, and date ranges
- **🔧 Modular Design**: Reusable functions with robust error handling
- **📊 Real-time Progress**: Live progress tracking with detailed status updates
- **🧹 Automatic Cleanup**: Safe temporary file management and cleanup
- **✅ Comprehensive Testing**: Built-in dry-run mode and validation
- **🔒 Enhanced Security**: Secure credential handling and input validation
- **🌐 Cross-platform**: Works on Windows, Linux, and macOS

## 🛠 Quick Start

### Basic Usage
```bash
# Back up today's data (yesterday for daily backups)
python tngd_backup.py

# Back up specific date
python tngd_backup.py 01 March 2025

# Back up date range
python tngd_backup.py --start "01 March 2025" --end "31 March 2025"

# Validate configuration (dry run)
python tngd_backup.py --dry-run

# Get help
python tngd_backup.py --help
```

## 📁 Project Structure
```
TNGD/
├── tngd_backup.py              # 🆕 Main unified backup script
├── test_tngd_backup.py         # 🆕 Comprehensive test suite
├── TNGD_UNIFIED_BACKUP_README.md # 🆕 Complete documentation
├── config.json                 # Configuration settings
├── tabletest/tables.json       # Table list configuration
├── core/                       # Core modules (streamlined)
│   ├── devo_client.py          # Devo API client
│   ├── storage_manager.py      # OSS storage management
│   ├── compression_service.py  # File compression
│   └── config_manager.py       # Configuration management
├── utils/                      # Essential utilities (minimal)
│   ├── error_handler.py        # Error handling
│   └── minimal_logging.py      # Logging utilities
└── logs/                       # Log files
```

## 📖 Migration from Legacy System

### What Changed
| **Before** | **After** | **Benefit** |
|------------|-----------|-------------|
| Multiple `.bat` files | Single `tngd_backup.py` | Simplified deployment |
| Complex batch logic | Clean Python CLI | Better error handling |
| Windows-only | Cross-platform | Broader compatibility |
| Manual progress tracking | Real-time progress | Better monitoring |

### Legacy Command Mapping
```bash
# OLD: run_daily_backup.bat
# NEW:
python tngd_backup.py

# OLD: run_monthly_backup.bat march 2025
# NEW:
python tngd_backup.py --start "01 March 2025" --end "31 March 2025"

# OLD: run_daily_backup.bat test dry-run
# NEW:
python tngd_backup.py --dry-run

# OLD: Single table backup scripts
# NEW:
python tngd_backup.py --tables my.app.tngd.waf
```

## 🧪 Testing & Validation

Run the comprehensive test suite:
```bash
python test_tngd_backup.py
```

Test specific functionality:
```bash
# Test configuration and connectivity
python tngd_backup.py --dry-run --verbose

# Test date parsing
python tngd_backup.py --dry-run 01 March 2025

# Test specific tables
python tngd_backup.py --dry-run --tables my.app.tngd.waf
```

## 📚 Documentation

- **[TNGD_UNIFIED_BACKUP_README.md](TNGD_UNIFIED_BACKUP_README.md)** - Complete documentation and usage guide
- **Built-in Help**: `python tngd_backup.py --help`
- **Test Suite**: `python test_tngd_backup.py`

## 🔧 Configuration

### Quick Setup Options

**Option 1: Interactive Setup (Recommended)**
```bash
python setup_env.py          # Interactive credential setup
python test_credentials.py   # Verify configuration
python tngd_backup.py --dry-run  # Test the system
```

**Option 2: Manual Setup**
```bash
cp .env.example .env         # Copy template
# Edit .env with your credentials
python tngd_backup.py --dry-run  # Test the system
```

### Configuration Files
- **`.env`** - 🔐 Secure credential storage (Devo API + OSS credentials)
- **`config.json`** - Main system configuration
- **`tabletest/tables.json`** - Table list configuration

### Required Credentials
```bash
# In your .env file:
DEVO_API_KEY=your_devo_api_key
DEVO_API_SECRET=your_devo_api_secret
DEVO_QUERY_ENDPOINT=https://api-us.devo.com/search/query

OSS_ACCESS_KEY_ID=your_oss_access_key_id
OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret
OSS_ENDPOINT=your_oss_endpoint
OSS_BUCKET_NAME=your_bucket_name
```

📖 **Detailed Setup Guide**: See [ENV_SETUP_GUIDE.md](ENV_SETUP_GUIDE.md)

## 🎯 Benefits of the Unified System

- ✅ **90% Reduction** in codebase complexity
- ✅ **Cross-platform** compatibility (Windows/Linux/macOS)
- ✅ **Real-time progress** tracking and monitoring
- ✅ **Enhanced security** with proper credential handling
- ✅ **Comprehensive testing** with built-in validation
- ✅ **Simplified maintenance** with single script deployment

---

> **📞 Support**: For issues or questions, run `python tngd_backup.py --dry-run --verbose` for diagnostics and check the comprehensive documentation in [TNGD_UNIFIED_BACKUP_README.md](TNGD_UNIFIED_BACKUP_README.md).
